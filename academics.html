<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Information System - Academic Records</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar" role="navigation" aria-label="Main navigation">
        <div class="nav-container">
            <div class="nav-brand">
                <h2><i class="fas fa-graduation-cap"></i> SIS Portal</h2>
            </div>
            <div class="nav-menu" id="navMenu">
                <a href="index.html" class="nav-link">Home</a>
                <a href="profile.html" class="nav-link">Student Profile</a>
                <a href="academics.html" class="nav-link active" aria-current="page">Academic Records</a>
                <a href="courses.html" class="nav-link">Course Modules</a>
            </div>
            <div class="nav-toggle" id="navToggle" aria-label="Toggle navigation menu">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero" role="banner">
        <div class="container">
            <h1>Academic Records</h1>
            <p>Comprehensive academic performance and grade history</p>
        </div>
    </section>

    <!-- Main Content -->
    <main class="main-content" role="main">
        <div class="container">
            <!-- Student Info Header -->
            <section class="student-header">
                <div class="student-info-card">
                    <div class="student-avatar">
                        <img id="studentAvatar" src="https://via.placeholder.com/80x80/3498db/ffffff?text=JS" alt="Student avatar">
                    </div>
                    <div class="student-details">
                        <h2 id="studentName">John Smith</h2>
                        <p id="studentUID">UID: SIS2024001</p>
                        <p id="studentProgram">Computer Science</p>
                    </div>
                    <div class="quick-actions">
                        <button class="btn-primary" onclick="window.location.href='profile.html'">
                            <i class="fas fa-user"></i> Open Details
                        </button>
                    </div>
                </div>
            </section>

            <!-- Academic Overview -->
            <section class="academic-overview">
                <h2>Academic Performance Overview</h2>
                <div class="overview-grid">
                    <div class="overview-card">
                        <i class="fas fa-trophy"></i>
                        <h3>Cumulative GPA</h3>
                        <p class="overview-value" id="cumulativeGPA">3.8</p>
                        <p class="overview-label">Out of 4.0</p>
                    </div>
                    <div class="overview-card">
                        <i class="fas fa-credit-card"></i>
                        <h3>Total Credits</h3>
                        <p class="overview-value">72</p>
                        <p class="overview-label">Completed</p>
                    </div>
                    <div class="overview-card">
                        <i class="fas fa-calendar-check"></i>
                        <h3>Semesters</h3>
                        <p class="overview-value">4</p>
                        <p class="overview-label">Completed</p>
                    </div>
                    <div class="overview-card">
                        <i class="fas fa-percentage"></i>
                        <h3>Completion Rate</h3>
                        <p class="overview-value">96%</p>
                        <p class="overview-label">Success Rate</p>
                    </div>
                </div>
            </section>

            <!-- Semester Records -->
            <section class="semester-records">
                <h2>Semester-wise Academic Records</h2>
                
                <!-- Fall 2023 -->
                <div class="semester-card">
                    <div class="semester-header">
                        <h3><i class="fas fa-calendar-alt"></i> Fall 2023</h3>
                        <div class="semester-gpa">
                            <span class="gpa-label">Semester GPA:</span>
                            <span class="gpa-value">3.9</span>
                        </div>
                    </div>
                    <div class="table-container">
                        <table class="grades-table" role="table">
                            <thead>
                                <tr>
                                    <th>Course Code</th>
                                    <th>Course Name</th>
                                    <th>Credits</th>
                                    <th>Grade</th>
                                    <th>Points</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>CS301</td>
                                    <td>Data Structures & Algorithms</td>
                                    <td>4</td>
                                    <td class="grade-a">A</td>
                                    <td>4.0</td>
                                </tr>
                                <tr>
                                    <td>CS302</td>
                                    <td>Database Management Systems</td>
                                    <td>3</td>
                                    <td class="grade-a">A-</td>
                                    <td>3.7</td>
                                </tr>
                                <tr>
                                    <td>CS303</td>
                                    <td>Software Engineering</td>
                                    <td>3</td>
                                    <td class="grade-a">A</td>
                                    <td>4.0</td>
                                </tr>
                                <tr>
                                    <td>MATH301</td>
                                    <td>Discrete Mathematics</td>
                                    <td>3</td>
                                    <td class="grade-b">B+</td>
                                    <td>3.3</td>
                                </tr>
                                <tr>
                                    <td>ENG201</td>
                                    <td>Technical Writing</td>
                                    <td>2</td>
                                    <td class="grade-a">A</td>
                                    <td>4.0</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Spring 2023 -->
                <div class="semester-card">
                    <div class="semester-header">
                        <h3><i class="fas fa-calendar-alt"></i> Spring 2023</h3>
                        <div class="semester-gpa">
                            <span class="gpa-label">Semester GPA:</span>
                            <span class="gpa-value">3.7</span>
                        </div>
                    </div>
                    <div class="table-container">
                        <table class="grades-table" role="table">
                            <thead>
                                <tr>
                                    <th>Course Code</th>
                                    <th>Course Name</th>
                                    <th>Credits</th>
                                    <th>Grade</th>
                                    <th>Points</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>CS201</td>
                                    <td>Object-Oriented Programming</td>
                                    <td>4</td>
                                    <td class="grade-a">A-</td>
                                    <td>3.7</td>
                                </tr>
                                <tr>
                                    <td>CS202</td>
                                    <td>Computer Networks</td>
                                    <td>3</td>
                                    <td class="grade-b">B+</td>
                                    <td>3.3</td>
                                </tr>
                                <tr>
                                    <td>CS203</td>
                                    <td>Operating Systems</td>
                                    <td>3</td>
                                    <td class="grade-a">A</td>
                                    <td>4.0</td>
                                </tr>
                                <tr>
                                    <td>MATH201</td>
                                    <td>Statistics</td>
                                    <td>3</td>
                                    <td class="grade-b">B</td>
                                    <td>3.0</td>
                                </tr>
                                <tr>
                                    <td>PHY201</td>
                                    <td>Physics for Engineers</td>
                                    <td>3</td>
                                    <td class="grade-a">A-</td>
                                    <td>3.7</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Fall 2022 -->
                <div class="semester-card">
                    <div class="semester-header">
                        <h3><i class="fas fa-calendar-alt"></i> Fall 2022</h3>
                        <div class="semester-gpa">
                            <span class="gpa-label">Semester GPA:</span>
                            <span class="gpa-value">3.8</span>
                        </div>
                    </div>
                    <div class="table-container">
                        <table class="grades-table" role="table">
                            <thead>
                                <tr>
                                    <th>Course Code</th>
                                    <th>Course Name</th>
                                    <th>Credits</th>
                                    <th>Grade</th>
                                    <th>Points</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>CS101</td>
                                    <td>Introduction to Programming</td>
                                    <td>4</td>
                                    <td class="grade-a">A</td>
                                    <td>4.0</td>
                                </tr>
                                <tr>
                                    <td>CS102</td>
                                    <td>Computer Science Fundamentals</td>
                                    <td>3</td>
                                    <td class="grade-a">A-</td>
                                    <td>3.7</td>
                                </tr>
                                <tr>
                                    <td>MATH101</td>
                                    <td>Calculus I</td>
                                    <td>4</td>
                                    <td class="grade-b">B+</td>
                                    <td>3.3</td>
                                </tr>
                                <tr>
                                    <td>ENG101</td>
                                    <td>English Composition</td>
                                    <td>3</td>
                                    <td class="grade-a">A</td>
                                    <td>4.0</td>
                                </tr>
                                <tr>
                                    <td>HIST101</td>
                                    <td>World History</td>
                                    <td>3</td>
                                    <td class="grade-a">A-</td>
                                    <td>3.7</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>

            <!-- GPA Trend -->
            <section class="gpa-trend">
                <h2>GPA Trend Analysis</h2>
                <div class="trend-card">
                    <div class="trend-chart">
                        <div class="chart-placeholder">
                            <i class="fas fa-chart-line"></i>
                            <p>GPA progression over semesters shows consistent performance with an upward trend in recent semesters.</p>
                        </div>
                    </div>
                    <div class="trend-stats">
                        <div class="trend-item">
                            <label>Highest Semester GPA:</label>
                            <span class="trend-value">3.9</span>
                        </div>
                        <div class="trend-item">
                            <label>Lowest Semester GPA:</label>
                            <span class="trend-value">3.7</span>
                        </div>
                        <div class="trend-item">
                            <label>Average GPA:</label>
                            <span class="trend-value">3.8</span>
                        </div>
                        <div class="trend-item">
                            <label>Improvement Trend:</label>
                            <span class="trend-value positive">****%</span>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer" role="contentinfo">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Contact Information</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> +****************</p>
                    <p><i class="fas fa-map-marker-alt"></i> 123 Education St, Learning City</p>
                </div>
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="profile.html">Student Profile</a></li>
                        <li><a href="academics.html">Academic Records</a></li>
                        <li><a href="courses.html">Course Modules</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Support</h3>
                    <ul>
                        <li><a href="#help">Help Center</a></li>
                        <li><a href="#privacy">Privacy Policy</a></li>
                        <li><a href="#terms">Terms of Service</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Student Information System. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
    <script>
        // Load student data when page loads
        document.addEventListener('DOMContentLoaded', function() {
            loadStudentAcademics();
        });

        function loadStudentAcademics() {
            const student = window.SIS.getCurrentStudent();
            
            // Update student header
            const imageColors = {
                '1': '3498db',
                '2': 'e74c3c',
                '3': '27ae60',
                '4': 'f39c12'
            };
            const initials = student.name.split(' ').map(n => n[0]).join('');
            const imageUrl = `https://via.placeholder.com/80x80/${imageColors[student.id]}/ffffff?text=${initials}`;
            
            document.getElementById('studentAvatar').src = imageUrl;
            document.getElementById('studentAvatar').alt = `${student.name} avatar`;
            document.getElementById('studentName').textContent = student.name;
            document.getElementById('studentUID').textContent = `UID: ${student.uid}`;
            document.getElementById('studentProgram').textContent = student.program;
            
            // Update cumulative GPA
            document.getElementById('cumulativeGPA').textContent = student.gpa;
        }
    </script>
</body>
</html>
