<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Information System - Profile</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar" role="navigation" aria-label="Main navigation">
        <div class="nav-container">
            <div class="nav-brand">
                <h2><i class="fas fa-graduation-cap"></i> SIS Portal</h2>
            </div>
            <div class="nav-menu" id="navMenu">
                <a href="index.html" class="nav-link">Home</a>
                <a href="profile.html" class="nav-link active" aria-current="page">Student Profile</a>
                <a href="academics.html" class="nav-link">Academic Records</a>
                <a href="courses.html" class="nav-link">Course Modules</a>
            </div>
            <div class="nav-toggle" id="navToggle" aria-label="Toggle navigation menu">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero" role="banner">
        <div class="container">
            <h1>Student Profile</h1>
            <p>Detailed student information and academic overview</p>
        </div>
    </section>

    <!-- Main Content -->
    <main class="main-content" role="main">
        <div class="container">
            <!-- Profile Header -->
            <section class="profile-header">
                <div class="profile-card">
                    <div class="profile-image">
                        <img id="studentImage" src="https://via.placeholder.com/200x200/3498db/ffffff?text=JS" alt="Student profile picture">
                    </div>
                    <div class="profile-info">
                        <h2 id="studentName">John Smith</h2>
                        <p class="student-uid" id="studentUID">UID: SIS2024001</p>
                        <p class="student-program" id="studentProgram">Computer Science</p>
                        <div class="profile-actions">
                            <button class="btn-primary" onclick="window.location.href='academics.html'">
                                <i class="fas fa-chart-line"></i> View Academic Records
                            </button>
                            <button class="btn-secondary" onclick="window.location.href='courses.html'">
                                <i class="fas fa-book"></i> View Courses
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Profile Details -->
            <section class="profile-details">
                <div class="details-grid">
                    <!-- Personal Information -->
                    <div class="detail-card">
                        <h3><i class="fas fa-user"></i> Personal Information</h3>
                        <div class="detail-item">
                            <label>Full Name:</label>
                            <span id="fullName">John Smith</span>
                        </div>
                        <div class="detail-item">
                            <label>Date of Birth:</label>
                            <span id="dateOfBirth">May 15, 1999</span>
                        </div>
                        <div class="detail-item">
                            <label>Age:</label>
                            <span id="age">24 years</span>
                        </div>
                        <div class="detail-item">
                            <label>Student ID:</label>
                            <span id="studentId">SIS2024001</span>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div class="detail-card">
                        <h3><i class="fas fa-envelope"></i> Contact Information</h3>
                        <div class="detail-item">
                            <label>Email:</label>
                            <span id="email"><EMAIL></span>
                        </div>
                        <div class="detail-item">
                            <label>Phone:</label>
                            <span id="phone">+****************</span>
                        </div>
                        <div class="detail-item">
                            <label>Address:</label>
                            <span id="address">123 Main St, City, State 12345</span>
                        </div>
                    </div>

                    <!-- Academic Information -->
                    <div class="detail-card">
                        <h3><i class="fas fa-graduation-cap"></i> Academic Information</h3>
                        <div class="detail-item">
                            <label>Program:</label>
                            <span id="program">Computer Science</span>
                        </div>
                        <div class="detail-item">
                            <label>Current Year:</label>
                            <span id="currentYear">Junior</span>
                        </div>
                        <div class="detail-item">
                            <label>Enrollment Date:</label>
                            <span id="enrollmentDate">September 1, 2022</span>
                        </div>
                        <div class="detail-item">
                            <label>Academic Advisor:</label>
                            <span id="advisor">Dr. Sarah Wilson</span>
                        </div>
                        <div class="detail-item">
                            <label>Current GPA:</label>
                            <span id="gpa" class="gpa-highlight">3.8</span>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="detail-card">
                        <h3><i class="fas fa-cogs"></i> Quick Actions</h3>
                        <div class="action-buttons">
                            <button class="btn-primary" onclick="window.location.href='academics.html'">
                                <i class="fas fa-chart-line"></i> Academic Records
                            </button>
                            <button class="btn-primary" onclick="window.location.href='courses.html'">
                                <i class="fas fa-book"></i> Course Modules
                            </button>
                            <button class="btn-secondary" onclick="window.location.href='index.html'">
                                <i class="fas fa-home"></i> Back to Home
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Academic Summary -->
            <section class="academic-summary">
                <h2>Academic Summary</h2>
                <div class="summary-grid">
                    <div class="summary-card">
                        <i class="fas fa-trophy"></i>
                        <h3>Current GPA</h3>
                        <p class="summary-value" id="summaryGPA">3.8</p>
                        <p class="summary-label">Out of 4.0</p>
                    </div>
                    <div class="summary-card">
                        <i class="fas fa-calendar-alt"></i>
                        <h3>Academic Year</h3>
                        <p class="summary-value" id="summaryYear">Junior</p>
                        <p class="summary-label">Current Status</p>
                    </div>
                    <div class="summary-card">
                        <i class="fas fa-book-open"></i>
                        <h3>Enrolled Courses</h3>
                        <p class="summary-value">6</p>
                        <p class="summary-label">This Semester</p>
                    </div>
                    <div class="summary-card">
                        <i class="fas fa-clock"></i>
                        <h3>Credit Hours</h3>
                        <p class="summary-value">18</p>
                        <p class="summary-label">This Semester</p>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer" role="contentinfo">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Contact Information</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> +****************</p>
                    <p><i class="fas fa-map-marker-alt"></i> 123 Education St, Learning City</p>
                </div>
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="profile.html">Student Profile</a></li>
                        <li><a href="academics.html">Academic Records</a></li>
                        <li><a href="courses.html">Course Modules</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Support</h3>
                    <ul>
                        <li><a href="#help">Help Center</a></li>
                        <li><a href="#privacy">Privacy Policy</a></li>
                        <li><a href="#terms">Terms of Service</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Student Information System. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
    <script>
        // Load student data when page loads
        document.addEventListener('DOMContentLoaded', function() {
            loadStudentProfile();
        });

        function loadStudentProfile() {
            const student = window.SIS.getCurrentStudent();
            
            // Update profile image
            const imageColors = {
                '1': '3498db',
                '2': 'e74c3c',
                '3': '27ae60',
                '4': 'f39c12'
            };
            const initials = student.name.split(' ').map(n => n[0]).join('');
            const imageUrl = `https://via.placeholder.com/200x200/${imageColors[student.id]}/ffffff?text=${initials}`;
            
            // Update all profile elements
            document.getElementById('studentImage').src = imageUrl;
            document.getElementById('studentImage').alt = `${student.name} profile picture`;
            document.getElementById('studentName').textContent = student.name;
            document.getElementById('studentUID').textContent = `UID: ${student.uid}`;
            document.getElementById('studentProgram').textContent = student.program;
            
            // Personal Information
            document.getElementById('fullName').textContent = student.name;
            document.getElementById('dateOfBirth').textContent = window.SIS.formatDate(student.dateOfBirth);
            document.getElementById('age').textContent = `${window.SIS.calculateAge(student.dateOfBirth)} years`;
            document.getElementById('studentId').textContent = student.uid;
            
            // Contact Information
            document.getElementById('email').textContent = student.email;
            document.getElementById('phone').textContent = student.phone;
            document.getElementById('address').textContent = student.address;
            
            // Academic Information
            document.getElementById('program').textContent = student.program;
            document.getElementById('currentYear').textContent = student.year;
            document.getElementById('enrollmentDate').textContent = window.SIS.formatDate(student.enrollmentDate);
            document.getElementById('advisor').textContent = student.advisor;
            document.getElementById('gpa').textContent = student.gpa;
            
            // Academic Summary
            document.getElementById('summaryGPA').textContent = student.gpa;
            document.getElementById('summaryYear').textContent = student.year;
        }
    </script>
</body>
</html>
