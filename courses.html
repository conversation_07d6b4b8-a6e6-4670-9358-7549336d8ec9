<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Information System - Course Modules</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar" role="navigation" aria-label="Main navigation">
        <div class="nav-container">
            <div class="nav-brand">
                <h2><i class="fas fa-graduation-cap"></i> SIS Portal</h2>
            </div>
            <div class="nav-menu" id="navMenu">
                <a href="index.html" class="nav-link">Home</a>
                <a href="profile.html" class="nav-link">Student Profile</a>
                <a href="academics.html" class="nav-link">Academic Records</a>
                <a href="courses.html" class="nav-link active" aria-current="page">Course Modules</a>
            </div>
            <div class="nav-toggle" id="navToggle" aria-label="Toggle navigation menu">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero" role="banner">
        <div class="container">
            <h1>Course Modules</h1>
            <p>Enrolled courses and detailed module information</p>
        </div>
    </section>

    <!-- Main Content -->
    <main class="main-content" role="main">
        <div class="container">
            <!-- Student Info Header -->
            <section class="student-header">
                <div class="student-info-card">
                    <div class="student-avatar">
                        <img id="studentAvatar" src="https://via.placeholder.com/80x80/3498db/ffffff?text=JS" alt="Student avatar">
                    </div>
                    <div class="student-details">
                        <h2 id="studentName">John Smith</h2>
                        <p id="studentUID">UID: SIS2024001</p>
                        <p id="studentProgram">Computer Science</p>
                    </div>
                    <div class="quick-actions">
                        <button class="btn-primary" onclick="window.location.href='profile.html'">
                            <i class="fas fa-user"></i> Open Details
                        </button>
                    </div>
                </div>
            </section>

            <!-- Course Summary -->
            <section class="course-summary">
                <h2>Current Semester Overview</h2>
                <div class="summary-grid">
                    <div class="summary-card">
                        <i class="fas fa-book"></i>
                        <h3>Enrolled Courses</h3>
                        <p class="summary-value">6</p>
                        <p class="summary-label">This Semester</p>
                    </div>
                    <div class="summary-card">
                        <i class="fas fa-clock"></i>
                        <h3>Credit Hours</h3>
                        <p class="summary-value">18</p>
                        <p class="summary-label">Total Credits</p>
                    </div>
                    <div class="summary-card">
                        <i class="fas fa-calendar-week"></i>
                        <h3>Class Hours</h3>
                        <p class="summary-value">24</p>
                        <p class="summary-label">Per Week</p>
                    </div>
                    <div class="summary-card">
                        <i class="fas fa-chart-line"></i>
                        <h3>Progress</h3>
                        <p class="summary-value">75%</p>
                        <p class="summary-label">Semester Complete</p>
                    </div>
                </div>
            </section>

            <!-- Current Courses -->
            <section class="current-courses">
                <h2>Current Semester Courses</h2>
                <div class="courses-grid">
                    <!-- Course 1 -->
                    <div class="course-card">
                        <div class="course-header">
                            <div class="course-icon">
                                <i class="fas fa-code"></i>
                            </div>
                            <div class="course-info">
                                <h3>Advanced Algorithms</h3>
                                <p class="course-code">CS401</p>
                                <p class="course-credits">4 Credits</p>
                            </div>
                        </div>
                        <div class="course-details">
                            <p class="course-description">
                                Advanced study of algorithmic techniques including dynamic programming, 
                                graph algorithms, and complexity analysis.
                            </p>
                            <div class="course-meta">
                                <div class="meta-item">
                                    <i class="fas fa-user-tie"></i>
                                    <span>Dr. Sarah Wilson</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-clock"></i>
                                    <span>MWF 10:00-11:00 AM</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Room CS-201</span>
                                </div>
                            </div>
                            <div class="course-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 80%"></div>
                                </div>
                                <span class="progress-text">80% Complete</span>
                            </div>
                        </div>
                        <div class="course-actions">
                            <button class="btn-primary" onclick="window.location.href='profile.html'">
                                <i class="fas fa-user"></i> Open Details
                            </button>
                        </div>
                    </div>

                    <!-- Course 2 -->
                    <div class="course-card">
                        <div class="course-header">
                            <div class="course-icon">
                                <i class="fas fa-database"></i>
                            </div>
                            <div class="course-info">
                                <h3>Machine Learning</h3>
                                <p class="course-code">CS402</p>
                                <p class="course-credits">3 Credits</p>
                            </div>
                        </div>
                        <div class="course-details">
                            <p class="course-description">
                                Introduction to machine learning algorithms, supervised and unsupervised learning, 
                                neural networks, and practical applications.
                            </p>
                            <div class="course-meta">
                                <div class="meta-item">
                                    <i class="fas fa-user-tie"></i>
                                    <span>Dr. Michael Chen</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-clock"></i>
                                    <span>TTh 2:00-3:30 PM</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Room CS-301</span>
                                </div>
                            </div>
                            <div class="course-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 70%"></div>
                                </div>
                                <span class="progress-text">70% Complete</span>
                            </div>
                        </div>
                        <div class="course-actions">
                            <button class="btn-primary" onclick="window.location.href='profile.html'">
                                <i class="fas fa-user"></i> Open Details
                            </button>
                        </div>
                    </div>

                    <!-- Course 3 -->
                    <div class="course-card">
                        <div class="course-header">
                            <div class="course-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="course-info">
                                <h3>Cybersecurity</h3>
                                <p class="course-code">CS403</p>
                                <p class="course-credits">3 Credits</p>
                            </div>
                        </div>
                        <div class="course-details">
                            <p class="course-description">
                                Comprehensive study of cybersecurity principles, threat analysis, 
                                cryptography, and security protocols.
                            </p>
                            <div class="course-meta">
                                <div class="meta-item">
                                    <i class="fas fa-user-tie"></i>
                                    <span>Dr. Lisa Anderson</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-clock"></i>
                                    <span>MW 1:00-2:30 PM</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Room CS-401</span>
                                </div>
                            </div>
                            <div class="course-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 75%"></div>
                                </div>
                                <span class="progress-text">75% Complete</span>
                            </div>
                        </div>
                        <div class="course-actions">
                            <button class="btn-primary" onclick="window.location.href='profile.html'">
                                <i class="fas fa-user"></i> Open Details
                            </button>
                        </div>
                    </div>

                    <!-- Course 4 -->
                    <div class="course-card">
                        <div class="course-header">
                            <div class="course-icon">
                                <i class="fas fa-mobile-alt"></i>
                            </div>
                            <div class="course-info">
                                <h3>Mobile App Development</h3>
                                <p class="course-code">CS404</p>
                                <p class="course-credits">3 Credits</p>
                            </div>
                        </div>
                        <div class="course-details">
                            <p class="course-description">
                                Development of mobile applications for iOS and Android platforms 
                                using modern frameworks and design patterns.
                            </p>
                            <div class="course-meta">
                                <div class="meta-item">
                                    <i class="fas fa-user-tie"></i>
                                    <span>Dr. Robert Kim</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-clock"></i>
                                    <span>TTh 10:00-11:30 AM</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Room CS-501</span>
                                </div>
                            </div>
                            <div class="course-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 65%"></div>
                                </div>
                                <span class="progress-text">65% Complete</span>
                            </div>
                        </div>
                        <div class="course-actions">
                            <button class="btn-primary" onclick="window.location.href='profile.html'">
                                <i class="fas fa-user"></i> Open Details
                            </button>
                        </div>
                    </div>

                    <!-- Course 5 -->
                    <div class="course-card">
                        <div class="course-header">
                            <div class="course-icon">
                                <i class="fas fa-project-diagram"></i>
                            </div>
                            <div class="course-info">
                                <h3>Software Project Management</h3>
                                <p class="course-code">CS405</p>
                                <p class="course-credits">3 Credits</p>
                            </div>
                        </div>
                        <div class="course-details">
                            <p class="course-description">
                                Project management methodologies, agile development, team collaboration, 
                                and software lifecycle management.
                            </p>
                            <div class="course-meta">
                                <div class="meta-item">
                                    <i class="fas fa-user-tie"></i>
                                    <span>Dr. Jennifer Lee</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-clock"></i>
                                    <span>F 9:00-12:00 PM</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Room BUS-101</span>
                                </div>
                            </div>
                            <div class="course-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 85%"></div>
                                </div>
                                <span class="progress-text">85% Complete</span>
                            </div>
                        </div>
                        <div class="course-actions">
                            <button class="btn-primary" onclick="window.location.href='profile.html'">
                                <i class="fas fa-user"></i> Open Details
                            </button>
                        </div>
                    </div>

                    <!-- Course 6 -->
                    <div class="course-card">
                        <div class="course-header">
                            <div class="course-icon">
                                <i class="fas fa-brain"></i>
                            </div>
                            <div class="course-info">
                                <h3>Artificial Intelligence</h3>
                                <p class="course-code">CS406</p>
                                <p class="course-credits">4 Credits</p>
                            </div>
                        </div>
                        <div class="course-details">
                            <p class="course-description">
                                Fundamentals of artificial intelligence including search algorithms, 
                                knowledge representation, and intelligent agents.
                            </p>
                            <div class="course-meta">
                                <div class="meta-item">
                                    <i class="fas fa-user-tie"></i>
                                    <span>Dr. David Park</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-clock"></i>
                                    <span>MWF 2:00-3:00 PM</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Room CS-601</span>
                                </div>
                            </div>
                            <div class="course-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 60%"></div>
                                </div>
                                <span class="progress-text">60% Complete</span>
                            </div>
                        </div>
                        <div class="course-actions">
                            <button class="btn-primary" onclick="window.location.href='profile.html'">
                                <i class="fas fa-user"></i> Open Details
                            </button>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer" role="contentinfo">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Contact Information</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> +****************</p>
                    <p><i class="fas fa-map-marker-alt"></i> 123 Education St, Learning City</p>
                </div>
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="profile.html">Student Profile</a></li>
                        <li><a href="academics.html">Academic Records</a></li>
                        <li><a href="courses.html">Course Modules</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Support</h3>
                    <ul>
                        <li><a href="#help">Help Center</a></li>
                        <li><a href="#privacy">Privacy Policy</a></li>
                        <li><a href="#terms">Terms of Service</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Student Information System. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
    <script>
        // Load student data when page loads
        document.addEventListener('DOMContentLoaded', function() {
            loadStudentCourses();
        });

        function loadStudentCourses() {
            const student = window.SIS.getCurrentStudent();
            
            // Update student header
            const imageColors = {
                '1': '3498db',
                '2': 'e74c3c',
                '3': '27ae60',
                '4': 'f39c12'
            };
            const initials = student.name.split(' ').map(n => n[0]).join('');
            const imageUrl = `https://via.placeholder.com/80x80/${imageColors[student.id]}/ffffff?text=${initials}`;
            
            document.getElementById('studentAvatar').src = imageUrl;
            document.getElementById('studentAvatar').alt = `${student.name} avatar`;
            document.getElementById('studentName').textContent = student.name;
            document.getElementById('studentUID').textContent = `UID: ${student.uid}`;
            document.getElementById('studentProgram').textContent = student.program;
        }
    </script>
</body>
</html>
