// Student Information System JavaScript

// Student data structure for future backend integration
const studentsData = {
    '1': {
        id: '1',
        name: '<PERSON>',
        uid: 'SIS2024001',
        program: 'Computer Science',
        email: '<EMAIL>',
        phone: '+****************',
        address: '123 Main St, City, State 12345',
        dateOfBirth: '1999-05-15',
        enrollmentDate: '2022-09-01',
        gpa: 3.8,
        year: 'Junior',
        advisor: 'Dr. <PERSON>'
    },
    '2': {
        id: '2',
        name: '<PERSON>',
        uid: 'SIS2024002',
        program: 'Information Technology',
        email: '<EMAIL>',
        phone: '+****************',
        address: '456 Oak Ave, City, State 12345',
        dateOfBirth: '2000-03-22',
        enrollmentDate: '2022-09-01',
        gpa: 3.9,
        year: 'Junior',
        advisor: 'Dr. <PERSON>'
    },
    '3': {
        id: '3',
        name: '<PERSON>',
        uid: 'SIS2024003',
        program: 'Software Engineering',
        email: '<EMAIL>',
        phone: '+****************',
        address: '789 Pine St, City, State 12345',
        dateOfBirth: '1998-11-08',
        enrollmentDate: '2021-09-01',
        gpa: 3.7,
        year: 'Senior',
        advisor: 'Dr. Lisa <PERSON>'
    },
    '4': {
        id: '4',
        name: '<PERSON> <PERSON>',
        uid: 'SIS2024004',
        program: 'Data Science',
        email: '<EMAIL>',
        phone: '+****************',
        address: '321 Elm St, City, State 12345',
        dateOfBirth: '1999-08-12',
        enrollmentDate: '2022-01-15',
        gpa: 3.6,
        year: 'Junior',
        advisor: 'Dr. Robert Kim'
    }
};

// DOM Content Loaded Event
document.addEventListener('DOMContentLoaded', function() {
    initializeNavigation();
    initializeAnimations();
    setActiveNavLink();
});

// Navigation functionality
function initializeNavigation() {
    const navToggle = document.getElementById('navToggle');
    const navMenu = document.getElementById('navMenu');

    if (navToggle && navMenu) {
        navToggle.addEventListener('click', function() {
            navMenu.classList.toggle('active');
            
            // Animate hamburger menu
            const spans = navToggle.querySelectorAll('span');
            spans.forEach((span, index) => {
                if (navMenu.classList.contains('active')) {
                    if (index === 0) span.style.transform = 'rotate(45deg) translate(5px, 5px)';
                    if (index === 1) span.style.opacity = '0';
                    if (index === 2) span.style.transform = 'rotate(-45deg) translate(7px, -6px)';
                } else {
                    span.style.transform = 'none';
                    span.style.opacity = '1';
                }
            });
        });

        // Close mobile menu when clicking on a link
        const navLinks = navMenu.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', () => {
                navMenu.classList.remove('active');
                const spans = navToggle.querySelectorAll('span');
                spans.forEach(span => {
                    span.style.transform = 'none';
                    span.style.opacity = '1';
                });
            });
        });
    }
}

// Set active navigation link based on current page
function setActiveNavLink() {
    const currentPage = window.location.pathname.split('/').pop() || 'index.html';
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === currentPage) {
            link.classList.add('active');
            link.setAttribute('aria-current', 'page');
        } else {
            link.removeAttribute('aria-current');
        }
    });
}

// Initialize animations and interactions
function initializeAnimations() {
    // Add intersection observer for fade-in animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe elements for animation
    const animatedElements = document.querySelectorAll('.student-box, .stat-card');
    animatedElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
}

// Open student details function
function openStudentDetails(studentId) {
    // Store selected student ID in localStorage for profile page
    localStorage.setItem('selectedStudentId', studentId);
    
    // Navigate to profile page
    window.location.href = 'profile.html';
}

// Get current student data (for use in other pages)
function getCurrentStudent() {
    const studentId = localStorage.getItem('selectedStudentId') || '1';
    return studentsData[studentId] || studentsData['1'];
}

// Utility function to format date
function formatDate(dateString) {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('en-US', options);
}

// Utility function to calculate age
function calculateAge(dateOfBirth) {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
    }
    
    return age;
}

// Search functionality (for future implementation)
function searchStudents(query) {
    const results = [];
    Object.values(studentsData).forEach(student => {
        if (student.name.toLowerCase().includes(query.toLowerCase()) ||
            student.uid.toLowerCase().includes(query.toLowerCase()) ||
            student.program.toLowerCase().includes(query.toLowerCase())) {
            results.push(student);
        }
    });
    return results;
}

// Export functions for use in other files
window.SIS = {
    openStudentDetails,
    getCurrentStudent,
    formatDate,
    calculateAge,
    searchStudents,
    studentsData
};
