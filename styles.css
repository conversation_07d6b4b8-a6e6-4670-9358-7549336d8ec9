/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation Styles */
.navbar {
    background-color: #2c3e50;
    color: white;
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand h2 {
    color: white;
    font-size: 1.5rem;
}

.nav-brand i {
    color: #3498db;
    margin-right: 0.5rem;
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

.nav-link {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
    background-color: #3498db;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background-color: white;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    text-align: center;
    padding: 4rem 0;
}

.hero h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    font-weight: 300;
}

.hero p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* Main Content */
.main-content {
    padding: 3rem 0;
}

.students-overview h2,
.quick-stats h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: #2c3e50;
    font-size: 2rem;
}

/* Student Grid */
.students-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.student-box {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.student-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.student-image img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    margin-bottom: 1rem;
    border: 4px solid #3498db;
}

.student-info h3 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 1.3rem;
}

.student-uid {
    color: #7f8c8d;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.student-program {
    color: #3498db;
    font-weight: 500;
    margin-bottom: 1.5rem;
}

/* Buttons */
.btn-primary {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.3s ease, transform 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary:hover {
    background-color: #2980b9;
    transform: translateY(-2px);
}

.btn-secondary {
    background-color: #95a5a6;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-secondary:hover {
    background-color: #7f8c8d;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-3px);
}

.stat-card i {
    font-size: 2.5rem;
    color: #3498db;
    margin-bottom: 1rem;
}

.stat-card h3 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #3498db;
}

/* Footer */
.footer {
    background-color: #2c3e50;
    color: white;
    padding: 3rem 0 1rem;
    margin-top: 4rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    margin-bottom: 1rem;
    color: #3498db;
}

.footer-section p,
.footer-section li {
    margin-bottom: 0.5rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: #3498db;
}

.footer-section i {
    margin-right: 0.5rem;
    color: #3498db;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #34495e;
    color: #bdc3c7;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
        flex-direction: column;
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background-color: #2c3e50;
        padding: 1rem;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        z-index: 999;
    }

    .nav-menu.active {
        display: flex;
    }

    .nav-toggle {
        display: flex;
    }

    .hero h1 {
        font-size: 2rem;
    }

    .hero p {
        font-size: 1rem;
    }

    .students-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    /* Profile and Academic Pages Mobile */
    .profile-card {
        flex-direction: column;
        text-align: center;
        gap: 2rem;
    }

    .profile-actions {
        justify-content: center;
    }

    .details-grid {
        grid-template-columns: 1fr;
    }

    .summary-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .student-info-card {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .quick-actions {
        margin-left: 0;
    }

    .overview-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .trend-card {
        grid-template-columns: 1fr;
    }

    .courses-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero {
        padding: 2rem 0;
    }

    .main-content {
        padding: 2rem 0;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus styles for accessibility */
.nav-link:focus,
.btn-primary:focus,
.btn-secondary:focus {
    outline: 2px solid #3498db;
    outline-offset: 2px;
}

/* Profile Page Styles */
.profile-header {
    margin-bottom: 3rem;
}

.profile-card {
    background: white;
    border-radius: 15px;
    padding: 3rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 3rem;
}

.profile-image img {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    border: 5px solid #3498db;
    object-fit: cover;
}

.profile-info h2 {
    color: #2c3e50;
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
}

.profile-actions {
    margin-top: 2rem;
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Profile Details */
.profile-details {
    margin-bottom: 3rem;
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.detail-card {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.detail-card:hover {
    transform: translateY(-3px);
}

.detail-card h3 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.detail-card h3 i {
    color: #3498db;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #ecf0f1;
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-item label {
    font-weight: 600;
    color: #7f8c8d;
    flex: 1;
}

.detail-item span {
    color: #2c3e50;
    flex: 2;
    text-align: right;
}

.gpa-highlight {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white !important;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-weight: bold;
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.action-buttons button {
    width: 100%;
    justify-content: center;
}

/* Academic Summary */
.academic-summary h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: #2c3e50;
    font-size: 2rem;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.summary-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.summary-card:hover {
    transform: translateY(-3px);
}

.summary-card i {
    font-size: 2.5rem;
    color: #3498db;
    margin-bottom: 1rem;
}

.summary-card h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.summary-value {
    font-size: 2.5rem;
    font-weight: bold;
    color: #3498db;
    margin-bottom: 0.5rem;
}

.summary-label {
    color: #7f8c8d;
    font-size: 0.9rem;
}

/* Academic Records Page Styles */
.student-header {
    margin-bottom: 3rem;
}

.student-info-card {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 2rem;
}

.student-avatar img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 3px solid #3498db;
}

.student-details h2 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.student-details p {
    color: #7f8c8d;
    margin-bottom: 0.25rem;
}

.quick-actions {
    margin-left: auto;
}

/* Academic Overview */
.academic-overview {
    margin-bottom: 3rem;
}

.academic-overview h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: #2c3e50;
    font-size: 2rem;
}

.overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.overview-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.overview-card:hover {
    transform: translateY(-3px);
}

.overview-card i {
    font-size: 2.5rem;
    color: #3498db;
    margin-bottom: 1rem;
}

.overview-card h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.overview-value {
    font-size: 2.5rem;
    font-weight: bold;
    color: #3498db;
    margin-bottom: 0.5rem;
}

.overview-label {
    color: #7f8c8d;
    font-size: 0.9rem;
}

/* Semester Records */
.semester-records h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: #2c3e50;
    font-size: 2rem;
}

.semester-card {
    background: white;
    border-radius: 10px;
    margin-bottom: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    overflow: hidden;
}

.semester-header {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.semester-header h3 {
    margin: 0;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.semester-gpa {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.gpa-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.gpa-value {
    font-size: 1.5rem;
    font-weight: bold;
    background: rgba(255,255,255,0.2);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
}

/* Tables */
.table-container {
    overflow-x: auto;
}

.grades-table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
}

.grades-table th,
.grades-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #ecf0f1;
}

.grades-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
}

.grades-table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Grade Colors */
.grade-a {
    color: #27ae60;
    font-weight: bold;
}

.grade-b {
    color: #f39c12;
    font-weight: bold;
}

.grade-c {
    color: #e67e22;
    font-weight: bold;
}

.grade-d {
    color: #e74c3c;
    font-weight: bold;
}

/* GPA Trend */
.gpa-trend h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: #2c3e50;
    font-size: 2rem;
}

.trend-card {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
}

.chart-placeholder {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 3rem;
    text-align: center;
    color: #7f8c8d;
}

.chart-placeholder i {
    font-size: 3rem;
    color: #3498db;
    margin-bottom: 1rem;
}

.trend-stats {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.trend-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #ecf0f1;
}

.trend-item:last-child {
    border-bottom: none;
}

.trend-item label {
    font-weight: 600;
    color: #7f8c8d;
}

.trend-value {
    color: #2c3e50;
    font-weight: bold;
}

.trend-value.positive {
    color: #27ae60;
}

/* Course Modules Page Styles */
.course-summary {
    margin-bottom: 3rem;
}

.course-summary h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: #2c3e50;
    font-size: 2rem;
}

/* Current Courses */
.current-courses h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: #2c3e50;
    font-size: 2rem;
}

.courses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.course-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.course-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.course-header {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.course-icon {
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.course-info h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.3rem;
}

.course-code {
    font-size: 0.9rem;
    opacity: 0.9;
    margin: 0;
}

.course-credits {
    font-size: 0.9rem;
    opacity: 0.9;
    margin: 0;
}

.course-details {
    padding: 1.5rem;
}

.course-description {
    color: #7f8c8d;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.course-meta {
    margin-bottom: 1.5rem;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    color: #7f8c8d;
    font-size: 0.9rem;
}

.meta-item i {
    color: #3498db;
    width: 16px;
}

.course-progress {
    margin-bottom: 1rem;
}

.progress-bar {
    background: #ecf0f1;
    border-radius: 10px;
    height: 8px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    background: linear-gradient(90deg, #3498db, #2980b9);
    height: 100%;
    border-radius: 10px;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 0.9rem;
    color: #7f8c8d;
    font-weight: 500;
}

.course-actions {
    padding: 0 1.5rem 1.5rem;
}

.course-actions button {
    width: 100%;
    justify-content: center;
}

/* Responsive Design for Profile and Academic Pages */
@media (max-width: 768px) {
    .profile-card {
        flex-direction: column;
        text-align: center;
        gap: 2rem;
    }

    .profile-actions {
        justify-content: center;
    }

    .details-grid {
        grid-template-columns: 1fr;
    }

    .summary-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .student-info-card {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .quick-actions {
        margin-left: 0;
    }

    .overview-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .trend-card {
        grid-template-columns: 1fr;
    }

    .courses-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .profile-card {
        padding: 2rem 1rem;
    }

    .profile-image img {
        width: 150px;
        height: 150px;
    }

    .profile-info h2 {
        font-size: 2rem;
    }

    .detail-card {
        padding: 1.5rem;
    }

    .detail-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .detail-item span {
        text-align: left;
    }

    .summary-grid,
    .overview-grid {
        grid-template-columns: 1fr;
    }

    .student-info-card {
        padding: 1.5rem;
    }

    .student-avatar img {
        width: 60px;
        height: 60px;
    }

    .grades-table {
        font-size: 0.9rem;
    }

    .grades-table th,
    .grades-table td {
        padding: 0.75rem 0.5rem;
    }

    .semester-header {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .course-card {
        margin-bottom: 1rem;
    }

    .course-header {
        padding: 1rem;
    }

    .course-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .course-details {
        padding: 1rem;
    }
}
